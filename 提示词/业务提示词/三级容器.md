# 三级容器

## 基于‘compnetA1.tsx’容器布局

**1.布局结构说明(logicA1.tsx):**

- 1.装载容器: 'componetA1'
- 2.存放组件: 'featureA1'
- 3.排列方式: 网格排列
  - 1.网格行数: 33
  - 2.网格列数: 33
- 4.组件间隔: 'margin: 1vW'
- 5.组件坐标: 计算以中心组件为原点的所有组件坐标

**2.功能组件1(featureA1.tsx):**

- 1.组件形状: 矩形
- 2.组件高度: ('componetA1'容器高度 - 网格行数 + 1 * 组件间隔) / 网格行数
- 3.组件宽度: ('componetA1'容器宽度 - 网格列数 + 1 * 组件间隔) / 网格列数
- 4.背景颜色:  #cecece
- 5.组件圆角: 5px
- 6.展示方式: 弹性布局
- 7.弹性方向: 垂直
- 8.对齐方式: 水平，垂直居中
- 9.溢出处理: 隐藏

## 基于‘compnetB1.tsx’容器布局

**1.布局结构说明(logicB1.tsx):**

- 1.排列方式: 功能容器在‘componetB1’容器内垂直排列
- 2.排列结构: [功能容器1]
             [功能容器2]
             [功能容器3]
             [功能容器4]
- 3.容器间隔: 通过[间隔]产生间隔
- 4.间隔:
  - 1.间隔属性: 'margin-top: 容器‘componetB1’的3%高度'
  - 2.间隔对象: 'featureB1_1'，'featureB1_2'，'featureB1_3', 'featureB1_4'

**2.功能容器2(featureB1_1.tsx):**

- 1.容器形状: 长方形
- 2.容器高度: 继承父容器‘componetB1’的15%高度
- 3.容器宽度: 继承父容器'componetB1'的94%宽度
- 4.背景颜色:  #bebebe
- 5.展示方式: 弹性布局
- 6.弹性方向: 水平
- 7.对齐方式: 水平，垂直居中
- 8.溢出处理: 隐藏
- 9.组件间隔: 顶部[间隔] * 2

**3.功能容器3(featureB1_2.tsx):**

- 1.容器形状: 长方形
- 2.容器高度: 继承父容器‘componetB1’的15%高度
- 3.容器宽度: 继承父容器'componetB1'的94%宽度
- 4.背景颜色:  #bebebe
- 5.展示方式: 弹性布局
- 6.弹性方向: 水平
- 7.对齐方式: 水平，垂直居中
- 8.溢出处理: 隐藏
- 9.组件间隔: 顶部[间隔]

**4.功能容器4(featureB1_3.tsx)**

- 1.容器形状: 长方形
- 2.容器高度: 继承父容器‘componetB1’的15%高度
- 3.容器宽度: 继承父容器'componetB1'的94%宽度
- 4.背景颜色:  #bebebe
- 5.展示方式: 弹性布局
- 6.弹性方向: 水平
- 7.对齐方式: 水平，垂直居中
- 8.溢出处理: 隐藏
- 9.组件间隔: 顶部[间隔]

**5.功能容器5(featureB1_4.tsx)**

- 1.容器形状: 长方形
- 2.容器高度: 继承父容器‘componetB1’的15%高度
- 3.容器宽度: 继承父容器'componetB1'的94%宽度
- 4.背景颜色:  #bebebe
- 5.展示方式: 弹性布局
- 6.弹性方向: 水平
- 7.对齐方式: 水平，垂直居中
- 8.溢出处理: 隐藏
- 9.组件间隔: 顶部[间隔]
