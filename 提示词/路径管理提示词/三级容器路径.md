# 三级容器路径

## 创建容器文件

**功能容器文件:**

- 1.创建功能容器1文件‘featureA1.tsx’ -> (根目录/frontend/features/feature)
- 2.创建功能容器2文件‘featureB1_1.tsx’ -> (根目录/frontend/features/feature)
- 3.创建功能容器3文件‘featureB1_2.tsx’ -> (根目录/frontend/features/feature)
- 4.创建功能容器4文件‘featureB1_3.tsx’ -> (根目录/frontend/features/feature)
- 5.创建功能容器5文件‘featureB1_4.tsx’ -> (根目录/frontend/features/feature)

**容器交互文件:**

- 1.创建功能容器交互文件‘logicA1.tsx’ -> (根目录/frontend/features/logic)
- 2.创建功能容器交互文件‘logicB1_1.tsx’ -> (根目录/frontend/features/logic)
- 3.创建功能容器交互文件‘logicB1_2.tsx’ -> (根目录/frontend/features/logic)
- 4.创建功能容器交互文件‘logicB1_3.tsx’ -> (根目录/frontend/features/logic)
- 5.创建功能容器交互文件‘logicB1_4.tsx’ -> (根目录/frontend/features/logic)
