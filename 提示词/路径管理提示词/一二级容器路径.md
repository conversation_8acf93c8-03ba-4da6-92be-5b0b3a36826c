# 一二级容器路径

## 创建容器文件

**主容器文件:**

- 1.创建文件‘main_container.tsx’ -> ('根目录/frontend/app)

**容器交互文件:**

- 1，创建文件‘compnet_interactionB.tsx’ -> ('根目录/frontend/componets/interaction')

**次容器文件:**

- 1.创建文件‘componetA1.tsx’ -> (''根目录/frontend/componets/componet')
- 2.创建文件‘componetB1.tsx’ -> (''根目录/frontend/componets/componet')
- 3.创建文件‘componetB2.tsx’ -> (''根目录/frontend/componets/componet')
- 4.创建文件‘componetButton.tsx’ -> (''根目录/frontend/componets/componet')
