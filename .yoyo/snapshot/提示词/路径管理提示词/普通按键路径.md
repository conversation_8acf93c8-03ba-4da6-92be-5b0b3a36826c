# 普通按键文件路径

## 创建按键文件

**文件夹:**

- 1.创建普通按键文件夹‘secondary’ -> (根目录/frontend/ButtonCodes)
  - 1.创建普通按键样式文件夹‘style’
  - 2.创建普通按键事件文件夹‘event’
  - 3.创建普通按键组件文件夹‘SecondaryButton’
  
- 2.创建Storybook普通按键文件夹‘secondary_stories’ -> (根目录/apps/frontend/stories)

**代码文件:**

- 1.创建普通按键样式文件‘style_secondary.ts’ -> (根目录/frontend/ButtonCodes/secondary/style)
- 2.创建普通按键事件文件‘event_secondary.ts’ -> (根目录/frontend/ButtonCodes/secondary/event)
- 3.创建普通按键组件文件‘SecondaryButton.tsx’ -> (根目录/frontend/ButtonCodes/secondary/SecondaryButton)
- 4.创建普通按键Storybook文件‘SecondaryButton.stories.tsx’ -> (根目录/apps/frontend/stories/secondary_stories)
