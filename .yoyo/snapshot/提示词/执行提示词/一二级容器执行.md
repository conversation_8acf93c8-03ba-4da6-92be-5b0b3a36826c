# 一二级容器

## 初始提示词

- 1.阅读‘前端技术栈.md’并用于构建代码
- 2.严格执行‘一二级容器.md’中的指示
- 3.禁止提前阅读除提示词描述以外的文档
- 4.仅完成该文本提供的逻辑流程，禁止补全
- 5.检测代码能否正常运行
- 6.构建gitignore文档

## 优化提示词

请按照以下步骤严格执行前端界面容器的构建任务：

1. **技术栈参考**：首先阅读项目根目录下的 `前端技术栈.md` 文件，了解项目使用的技术栈、框架版本、依赖包等信息，并严格按照文档中的技术规范来编写代码
2. **需求执行**：严格按照 `一二级容器.md` 文件中的具体指示和要求来实现界面容器功能，不得偏离文档中的设计规范和功能要求
3. **文档限制**：在执行过程中，除了上述两个指定的提示词文档外，禁止主动阅读项目中的其他文档文件，避免受到无关信息的干扰
4. **范围限制**：严格按照当前任务的逻辑流程执行，不得自行添加额外功能、优化建议或扩展实现，只完成明确要求的功能
5. **代码验证**：完成代码编写后，必须进行功能测试，确保代码能够正常运行，没有语法错误、依赖缺失或运行时错误
6. **项目配置**：根据项目的技术栈和依赖情况，创建或更新 `.gitignore` 文件，确保忽略不必要的文件（如 node_modules、构建产物、IDE配置文件等）

请确认理解上述要求后开始执行任务。
